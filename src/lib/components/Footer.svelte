<script lang="ts">
	const currentYear = new Date().getFullYear();

	const navigationLinks = [
		{ href: '/', label: 'Home' },
		{ href: '/about', label: 'About' },
		{ href: '/services', label: 'Services' },
		{ href: '/work', label: 'Work' },
		{ href: '/contact', label: 'Contact' }
	];

	const socialLinks = [
		{
			href: 'https://linkedin.com/company/hitez',
			label: 'LinkedIn',
			icon: 'M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z'
		},
		{
			href: 'https://instagram.com/hitez.agency',
			label: 'Instagram',
			icon: 'M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z'
		},
		{
			href: 'https://tiktok.com/@hitez.agency',
			label: 'TikTok',
			icon: 'M19.59 6.69a4.83 4.83 0 01-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 01-5.2 1.74 2.89 2.89 0 012.31-4.64 2.93 2.93 0 01.88.13V9.4a6.84 6.84 0 00-.88-.05A6.33 6.33 0 005 20.1a6.34 6.34 0 0010.86-4.43v-7a8.16 8.16 0 004.77 1.52v-3.4a4.85 4.85 0 01-1-.1z'
		},
		{
			href: 'https://behance.net/hitez',
			label: 'Behance',
			icon: 'M6.938 4.503c.702 0 1.34.06 1.92.188.577.13 1.07.33 1.485.61.41.28.733.65.96 1.12.225.47.34 1.05.34 1.73 0 .74-.17 1.36-.507 1.86-.338.5-.837.9-1.502 1.22.906.26 1.576.72 2.022 1.37.448.66.665 1.45.665 2.36 0 .75-.13 1.39-.41 1.93-.28.55-.67 1-1.16 1.35-.48.348-1.05.6-1.67.76-.62.16-1.25.24-1.89.24H0V4.51h6.938v-.007zM3.495 8.42h2.876c.62 0 1.11-.13 1.47-.4.36-.27.54-.68.54-1.23 0-.55-.18-.96-.54-1.23-.36-.27-.85-.4-1.47-.4H3.495v3.26zm0 5.54h3.18c.3 0 .59-.04.86-.12.27-.08.51-.2.72-.36.21-.16.37-.37.48-.63.11-.26.17-.56.17-.89 0-.67-.17-1.18-.52-1.53-.35-.35-.83-.53-1.44-.53H3.495v4.06zm17.37-2.15c.02.32-.02.64-.08.95H16.93c.05.4.2.72.44.96.24.24.54.36.89.36.25 0 .48-.05.69-.15.21-.1.39-.25.54-.45h2.01c-.2.68-.61 1.24-1.17 1.68-.56.44-1.25.66-2.07.66-.48 0-.91-.08-1.3-.24-.39-.16-.73-.38-1.01-.67-.28-.29-.5-.63-.65-1.02-.15-.39-.23-.81-.23-1.26 0-.45.08-.87.23-1.26.15-.39.37-.73.65-1.02.28-.29.62-.51 1.01-.67.39-.16.82-.24 1.3-.24.45 0 .86.07 1.23.22.37.15.69.36.96.63.27.27.48.58.63.94.15.36.23.75.23 1.17zm-2.01-.8c0-.25-.09-.47-.26-.66-.17-.19-.39-.28-.68-.28-.14 0-.27.02-.39.07-.12.05-.23.12-.32.21-.09.09-.16.2-.21.32-.05.12-.08.25-.09.39h1.95zm-3.46-2.83h2.2v-.76c0-.4-.11-.73-.32-.98-.21-.25-.5-.37-.87-.37-.19 0-.36.04-.51.13-.15.09-.27.21-.36.37-.09.16-.14.34-.16.55-.02.21-.03.43-.03.66v.4h.05zm-6.02-1.13c0-.8.16-1.54.47-2.23.31-.69.74-1.3 1.29-1.82.55-.52 1.2-.93 1.95-1.23.75-.3 1.56-.45 2.43-.45.87 0 1.68.15 **********.3 1.4.71 1.95 **********.98 1.13 1.29 **********.47 1.43.47 2.23 0 .8-.16 1.54-.47 2.23-.31.69-.74 1.3-1.29 1.82-.55.52-1.2.93-1.95 1.23-.75.3-1.56.45-2.43.45-.87 0-1.68-.15-2.43-.45-.75-.3-1.4-.71-1.95-1.23-.55-.52-.98-1.13-1.29-1.82-.31-.69-.47-1.43-.47-2.23z'
		}
	];

	const contactInfo = {
		email: '<EMAIL>',
		phone: '+84 123 456 789',
		address: 'Ho Chi Minh City, Vietnam'
	};
</script>

<footer class="bg-darker border-t border-accent/20 mt-20">
	<div class="container mx-auto px-6 py-16">
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
			<!-- Column 1: Logo and Slogan -->
			<div class="space-y-4">
				<div class="text-2xl font-bold text-accent font-heading">HITEZ</div>
				<p class="text-text-muted text-sm leading-relaxed">
					Born Digital.<br />Built Creative.
				</p>
				<p class="text-text-muted text-sm">
					A new generation of creators, blending technology and art to build unforgettable
					experiences.
				</p>
			</div>

			<!-- Column 2: Navigation Links -->
			<div class="space-y-4">
				<h4 class="text-text font-semibold font-heading">Navigation</h4>
				<ul class="space-y-2">
					{#each navigationLinks as link}
						<li>
							<a
								href={link.href}
								class="text-text-muted hover:text-accent transition-colors duration-300 text-sm"
							>
								{link.label}
							</a>
						</li>
					{/each}
				</ul>
			</div>

			<!-- Column 3: Social Media -->
			<div class="space-y-4">
				<h4 class="text-text font-semibold font-heading">Follow Us</h4>
				<div class="flex space-x-4">
					{#each socialLinks as social}
						<a
							href={social.href}
							target="_blank"
							rel="noopener noreferrer"
							class="w-8 h-8 flex items-center justify-center hover:text-accent transition-colors duration-300 hover-lift"
							aria-label={social.label}
						>
							<svg class="w-5 h-5 fill-current" viewBox="0 0 24 24">
								<path d={social.icon} />
							</svg>
						</a>
					{/each}
				</div>
			</div>

			<!-- Column 4: Contact Info -->
			<div class="space-y-4">
				<h4 class="text-text font-semibold font-heading">Contact</h4>
				<div class="space-y-2 text-sm">
					<a
						href="mailto:{contactInfo.email}"
						class="text-text-muted hover:text-accent transition-colors duration-300 block"
					>
						{contactInfo.email}
					</a>
					<a
						href="tel:{contactInfo.phone}"
						class="text-text-muted hover:text-accent transition-colors duration-300 block"
					>
						{contactInfo.phone}
					</a>
					<p class="text-text-muted">{contactInfo.address}</p>
				</div>
			</div>
		</div>

		<!-- Bottom Section -->
		<div class="border-t border-accent/20 mt-12 pt-8">
			<div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
				<p class="text-text-muted text-sm">
					© {currentYear} Hitez. All rights reserved.
				</p>
				<div class="flex space-x-6 text-sm">
					<a href="/privacy" class="text-text-muted hover:text-accent transition-colors duration-300">
						Privacy Policy
					</a>
					<a href="/terms" class="text-text-muted hover:text-accent transition-colors duration-300">
						Terms of Service
					</a>
				</div>
			</div>
		</div>
	</div>
</footer>
