<script lang="ts">
	import { onMount } from 'svelte';

	let cursorDot: HTMLElement;
	let cursorOutline: HTMLElement;
	let isHovering = $state(false);

	onMount(() => {
		const moveCursor = (e: MouseEvent) => {
			const { clientX: x, clientY: y } = e;

			if (cursorDot) {
				cursorDot.style.left = x + 'px';
				cursorDot.style.top = y + 'px';
			}

			if (cursorOutline) {
				cursorOutline.style.left = x + 'px';
				cursorOutline.style.top = y + 'px';
			}
		};

		const handleMouseEnter = () => {
			isHovering = true;
		};

		const handleMouseLeave = () => {
			isHovering = false;
		};

		// Add event listeners
		document.addEventListener('mousemove', moveCursor);

		// Add hover effects for interactive elements
		const interactiveElements = document.querySelectorAll('a, button, .hover-lift, .btn');
		interactiveElements.forEach((el) => {
			el.addEventListener('mouseenter', handleMouseEnter);
			el.addEventListener('mouseleave', handleMouseLeave);
		});

		return () => {
			document.removeEventListener('mousemove', moveCursor);
			interactiveElements.forEach((el) => {
				el.removeEventListener('mouseenter', handleMouseEnter);
				el.removeEventListener('mouseleave', handleMouseLeave);
			});
		};
	});
</script>

<!-- Custom Cursor -->
<div
	bind:this={cursorDot}
	class="cursor-dot fixed w-2 h-2 bg-accent rounded-full pointer-events-none z-50 transition-transform duration-150 ease-out"
	class:scale-150={isHovering}
	style="transform: translate(-50%, -50%);"
></div>

<div
	bind:this={cursorOutline}
	class="cursor-outline fixed w-8 h-8 border border-accent/50 rounded-full pointer-events-none z-50 transition-all duration-300 ease-out"
	class:scale-150={isHovering}
	class:border-accent={isHovering}
	style="transform: translate(-50%, -50%);"
></div>

<style>
	@media (hover: none) {
		.cursor-dot,
		.cursor-outline {
			display: none;
		}
	}
</style>
