<script lang="ts">
	import Button from '$lib/components/Button.svelte';
	import AnimatedCounter from '$lib/components/AnimatedCounter.svelte';

	const stats = [
		{ number: 50, suffix: '+', label: 'Projects Completed' },
		{ number: 25, suffix: '+', label: 'Happy Clients' },
		{ number: 3, suffix: '', label: 'Years of Innovation' },
		{ number: 100, suffix: '%', label: 'Digital Native' }
	];

	const values = [
		{
			title: 'Authenticity First',
			description: 'We believe in genuine connections over flashy gimmicks. Every experience we create comes from a place of truth.',
			icon: '💎'
		},
		{
			title: 'Innovation by Default',
			description: 'Being digital natives means we don\'t just use technology - we think in it. Innovation isn\'t a buzzword, it\'s our language.',
			icon: '🚀'
		},
		{
			title: 'Collaborative Spirit',
			description: 'We work with you, not for you. Your vision combined with our expertise creates magic that neither could achieve alone.',
			icon: '🤝'
		},
		{
			title: 'Sustainable Impact',
			description: 'Every project should leave the world a little better. We consider the environmental and social impact of everything we create.',
			icon: '🌱'
		}
	];

	const team = [
		{
			name: '<PERSON>',
			role: 'Creative Director',
			bio: 'Visionary leader with a passion for blending art and technology.',
			image: '/api/placeholder/300/300'
		},
		{
			name: 'Sam Tran',
			role: 'Technical Lead',
			bio: 'Code wizard who makes impossible ideas come to life.',
			image: '/api/placeholder/300/300'
		},
		{
			name: 'Jordan Le',
			role: 'Experience Designer',
			bio: 'Master of human-centered design and user psychology.',
			image: '/api/placeholder/300/300'
		},
		{
			name: 'Casey Pham',
			role: 'Innovation Strategist',
			bio: 'Future-focused thinker who spots trends before they happen.',
			image: '/api/placeholder/300/300'
		}
	];
</script>

<svelte:head>
	<title>About Us - Hitez</title>
	<meta name="description" content="Meet the Gen Z team behind Hitez. We're not just another agency - we're digital natives creating the future of experiences." />
</svelte:head>

<!-- Hero Section -->
<section class="pt-32 pb-20 px-6 relative overflow-hidden">
	<div class="absolute inset-0 bg-gradient-to-br from-primary via-dark to-primary">
		<div class="absolute top-1/4 right-1/4 w-96 h-96 bg-accent/5 rounded-full blur-3xl float"></div>
	</div>
	
	<div class="container mx-auto max-w-4xl text-center relative z-10">
		<h1 class="text-5xl md:text-7xl font-black mb-8 fade-in">
			We're Not Just Another <span class="text-accent gradient-text">Agency</span>
		</h1>
		<p class="text-xl md:text-2xl text-text-muted mb-8 max-w-3xl mx-auto slide-up">
			We're the generation that grew up with smartphones, learned on YouTube, and built our first businesses on social media. 
			This isn't just our job - it's our native language.
		</p>
	</div>
</section>

<!-- Stats Section -->
<section class="py-20 px-6 bg-dark">
	<div class="container mx-auto max-w-6xl">
		<div class="grid grid-cols-2 md:grid-cols-4 gap-8">
			{#each stats as stat, index}
				<div class="text-center slide-up" style="animation-delay: {index * 0.1}s;">
					<div class="text-4xl md:text-5xl font-black mb-2">
						<AnimatedCounter target={stat.number} suffix={stat.suffix} />
					</div>
					<p class="text-text-muted font-medium">{stat.label}</p>
				</div>
			{/each}
		</div>
	</div>
</section>

<!-- Story Section -->
<section class="py-20 px-6">
	<div class="container mx-auto max-w-4xl">
		<div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
			<div class="slide-in-left">
				<h2 class="text-4xl md:text-5xl font-bold mb-6">Our Story</h2>
				<div class="space-y-6 text-lg text-text-muted leading-relaxed">
					<p>
						Hitez was born from a simple realization: the creative industry was stuck in the past, 
						while the world had moved digital-first. We saw agencies trying to adapt to new technologies 
						instead of thinking natively in them.
					</p>
					<p>
						As Gen Z creators, we don't see AR, VR, or interactive installations as "new tech" - 
						they're just tools in our creative toolkit. We grew up in virtual worlds, learned through 
						interactive media, and built communities in digital spaces.
					</p>
					<p>
						That's why our work feels different. We're not translating analog ideas into digital formats. 
						We're creating experiences that could only exist in our connected, interactive world.
					</p>
				</div>
			</div>
			<div class="slide-in-right">
				<div class="relative">
					<div class="aspect-square bg-gradient-to-br from-accent/20 to-accent/5 rounded-lg flex items-center justify-center">
						<div class="text-8xl opacity-50">🎯</div>
					</div>
					<div class="absolute -bottom-4 -right-4 w-24 h-24 bg-accent/20 rounded-full blur-xl"></div>
				</div>
			</div>
		</div>
	</div>
</section>

<!-- Values Section -->
<section class="py-20 px-6 bg-dark">
	<div class="container mx-auto max-w-6xl">
		<div class="text-center mb-16 slide-up">
			<h2 class="text-4xl md:text-5xl font-bold mb-6">What Drives Us</h2>
			<p class="text-xl text-text-muted max-w-2xl mx-auto">
				Our values aren't just words on a wall - they're the principles that guide every decision we make.
			</p>
		</div>
		
		<div class="grid grid-cols-1 md:grid-cols-2 gap-8">
			{#each values as value, index}
				<div class="slide-up hover-lift" style="animation-delay: {index * 0.1}s;">
					<div class="bg-primary p-8 rounded-lg border border-accent/20 hover:border-accent/40 transition-all duration-300 h-full">
						<div class="text-4xl mb-4">{value.icon}</div>
						<h3 class="text-2xl font-bold mb-4 text-accent">{value.title}</h3>
						<p class="text-text-muted leading-relaxed">{value.description}</p>
					</div>
				</div>
			{/each}
		</div>
	</div>
</section>

<!-- Team Section -->
<section class="py-20 px-6">
	<div class="container mx-auto max-w-6xl">
		<div class="text-center mb-16 slide-up">
			<h2 class="text-4xl md:text-5xl font-bold mb-6">Meet the Team</h2>
			<p class="text-xl text-text-muted max-w-2xl mx-auto">
				The creative minds behind every unforgettable experience.
			</p>
		</div>
		
		<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
			{#each team as member, index}
				<div class="slide-up hover-lift group" style="animation-delay: {index * 0.1}s;">
					<div class="text-center">
						<div class="relative mb-6">
							<div class="aspect-square bg-gradient-to-br from-accent/20 to-accent/5 rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform duration-300">
								<div class="text-6xl opacity-50">👤</div>
							</div>
							<div class="absolute -bottom-2 -right-2 w-8 h-8 bg-accent rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
						</div>
						<h3 class="text-xl font-bold mb-2 group-hover:text-accent transition-colors duration-300">{member.name}</h3>
						<p class="text-accent text-sm font-medium mb-3">{member.role}</p>
						<p class="text-text-muted text-sm leading-relaxed">{member.bio}</p>
					</div>
				</div>
			{/each}
		</div>
	</div>
</section>

<!-- CTA Section -->
<section class="py-20 px-6 bg-dark">
	<div class="container mx-auto max-w-4xl text-center">
		<div class="slide-up">
			<h2 class="text-4xl md:text-5xl font-bold mb-6">
				Ready to Create Something <span class="text-accent">Extraordinary?</span>
			</h2>
			<p class="text-xl text-text-muted mb-8 max-w-2xl mx-auto">
				Let's combine your vision with our digital-native perspective to build experiences that matter.
			</p>
			<div class="space-y-4 md:space-y-0 md:space-x-4 md:flex md:justify-center">
				<Button href="/contact" variant="primary" size="lg">
					Start a Project
				</Button>
				<Button href="/work" variant="outline" size="lg">
					See Our Work
				</Button>
			</div>
		</div>
	</div>
</section>
