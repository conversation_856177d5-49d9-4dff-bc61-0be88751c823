
// this file is generated — do not edit it


/// <reference types="@sveltejs/kit" />

/**
 * Environment variables [loaded by Vite](https://vitejs.dev/guide/env-and-mode.html#env-files) from `.env` files and `process.env`. Like [`$env/dynamic/private`](https://svelte.dev/docs/kit/$env-dynamic-private), this module cannot be imported into client-side code. This module only includes variables that _do not_ begin with [`config.kit.env.publicPrefix`](https://svelte.dev/docs/kit/configuration#env) _and do_ start with [`config.kit.env.privatePrefix`](https://svelte.dev/docs/kit/configuration#env) (if configured).
 * 
 * _Unlike_ [`$env/dynamic/private`](https://svelte.dev/docs/kit/$env-dynamic-private), the values exported from this module are statically injected into your bundle at build time, enabling optimisations like dead code elimination.
 * 
 * ```ts
 * import { API_KEY } from '$env/static/private';
 * ```
 * 
 * Note that all environment variables referenced in your code should be declared (for example in an `.env` file), even if they don't have a value until the app is deployed:
 * 
 * ```
 * MY_FEATURE_FLAG=""
 * ```
 * 
 * You can override `.env` values from the command line like so:
 * 
 * ```bash
 * MY_FEATURE_FLAG="enabled" npm run dev
 * ```
 */
declare module '$env/static/private' {
	export const GREP_COLOR: string;
	export const LESS_TERMCAP_mb: string;
	export const TERM_PROGRAM: string;
	export const LESS_TERMCAP_md: string;
	export const FNM_LOGLEVEL: string;
	export const NODE: string;
	export const LESS_TERMCAP_me: string;
	export const ANDROID_HOME: string;
	export const PYENV_ROOT: string;
	export const INIT_CWD: string;
	export const SHELL: string;
	export const TERM: string;
	export const CLICOLOR: string;
	export const FNM_NODE_DIST_MIRROR: string;
	export const TMPDIR: string;
	export const HOMEBREW_REPOSITORY: string;
	export const CONDA_SHLVL: string;
	export const npm_config_global_prefix: string;
	export const TERM_PROGRAM_VERSION: string;
	export const CONDA_PROMPT_MODIFIER: string;
	export const FPATH: string;
	export const MallocNanoZone: string;
	export const ORIGINAL_XDG_CURRENT_DESKTOP: string;
	export const ZDOTDIR: string;
	export const LESS_TERMCAP_ue: string;
	export const COLOR: string;
	export const npm_config_noproxy: string;
	export const PNPM_HOME: string;
	export const npm_config_local_prefix: string;
	export const FNM_COREPACK_ENABLED: string;
	export const USER: string;
	export const COMMAND_MODE: string;
	export const GREP_COLORS: string;
	export const CONDA_EXE: string;
	export const npm_config_globalconfig: string;
	export const SSH_AUTH_SOCK: string;
	export const __CF_USER_TEXT_ENCODING: string;
	export const VSCODE_PROFILE_INITIALIZED: string;
	export const npm_execpath: string;
	export const PAGER: string;
	export const LESS_TERMCAP_us: string;
	export const LSCOLORS: string;
	export const FNM_VERSION_FILE_STRATEGY: string;
	export const _CE_CONDA: string;
	export const FNM_ARCH: string;
	export const PATH: string;
	export const npm_config_engine_strict: string;
	export const _: string;
	export const npm_package_json: string;
	export const __CFBundleIdentifier: string;
	export const USER_ZDOTDIR: string;
	export const CONDA_PREFIX: string;
	export const npm_config_init_module: string;
	export const npm_config_userconfig: string;
	export const PWD: string;
	export const npm_command: string;
	export const EDITOR: string;
	export const npm_lifecycle_event: string;
	export const npm_package_name: string;
	export const LANG: string;
	export const XPC_FLAGS: string;
	export const VSCODE_GIT_ASKPASS_EXTRA_ARGS: string;
	export const FNM_MULTISHELL_PATH: string;
	export const npm_config_npm_version: string;
	export const npm_config_node_gyp: string;
	export const npm_package_version: string;
	export const XPC_SERVICE_NAME: string;
	export const _CE_M: string;
	export const VSCODE_INJECTION: string;
	export const HOME: string;
	export const SHLVL: string;
	export const PYENV_SHELL: string;
	export const VSCODE_GIT_ASKPASS_MAIN: string;
	export const GOROOT: string;
	export const HOMEBREW_PREFIX: string;
	export const FNM_DIR: string;
	export const LOGNAME: string;
	export const VSCODEPATH: string;
	export const CONDA_PYTHON_EXE: string;
	export const npm_config_cache: string;
	export const npm_lifecycle_script: string;
	export const VSCODE_GIT_IPC_HANDLE: string;
	export const LESS_TERMCAP_so: string;
	export const GOPATH: string;
	export const CONDA_DEFAULT_ENV: string;
	export const FNM_RESOLVE_ENGINES: string;
	export const npm_config_user_agent: string;
	export const GIT_ASKPASS: string;
	export const VSCODE_GIT_ASKPASS_NODE: string;
	export const HOMEBREW_CELLAR: string;
	export const INFOPATH: string;
	export const COLORTERM: string;
	export const LESS_TERMCAP_se: string;
	export const npm_config_prefix: string;
	export const npm_node_execpath: string;
}

/**
 * Similar to [`$env/static/private`](https://svelte.dev/docs/kit/$env-static-private), except that it only includes environment variables that begin with [`config.kit.env.publicPrefix`](https://svelte.dev/docs/kit/configuration#env) (which defaults to `PUBLIC_`), and can therefore safely be exposed to client-side code.
 * 
 * Values are replaced statically at build time.
 * 
 * ```ts
 * import { PUBLIC_BASE_URL } from '$env/static/public';
 * ```
 */
declare module '$env/static/public' {
	
}

/**
 * This module provides access to runtime environment variables, as defined by the platform you're running on. For example if you're using [`adapter-node`](https://github.com/sveltejs/kit/tree/main/packages/adapter-node) (or running [`vite preview`](https://svelte.dev/docs/kit/cli)), this is equivalent to `process.env`. This module only includes variables that _do not_ begin with [`config.kit.env.publicPrefix`](https://svelte.dev/docs/kit/configuration#env) _and do_ start with [`config.kit.env.privatePrefix`](https://svelte.dev/docs/kit/configuration#env) (if configured).
 * 
 * This module cannot be imported into client-side code.
 * 
 * Dynamic environment variables cannot be used during prerendering.
 * 
 * ```ts
 * import { env } from '$env/dynamic/private';
 * console.log(env.DEPLOYMENT_SPECIFIC_VARIABLE);
 * ```
 * 
 * > In `dev`, `$env/dynamic` always includes environment variables from `.env`. In `prod`, this behavior will depend on your adapter.
 */
declare module '$env/dynamic/private' {
	export const env: {
		GREP_COLOR: string;
		LESS_TERMCAP_mb: string;
		TERM_PROGRAM: string;
		LESS_TERMCAP_md: string;
		FNM_LOGLEVEL: string;
		NODE: string;
		LESS_TERMCAP_me: string;
		ANDROID_HOME: string;
		PYENV_ROOT: string;
		INIT_CWD: string;
		SHELL: string;
		TERM: string;
		CLICOLOR: string;
		FNM_NODE_DIST_MIRROR: string;
		TMPDIR: string;
		HOMEBREW_REPOSITORY: string;
		CONDA_SHLVL: string;
		npm_config_global_prefix: string;
		TERM_PROGRAM_VERSION: string;
		CONDA_PROMPT_MODIFIER: string;
		FPATH: string;
		MallocNanoZone: string;
		ORIGINAL_XDG_CURRENT_DESKTOP: string;
		ZDOTDIR: string;
		LESS_TERMCAP_ue: string;
		COLOR: string;
		npm_config_noproxy: string;
		PNPM_HOME: string;
		npm_config_local_prefix: string;
		FNM_COREPACK_ENABLED: string;
		USER: string;
		COMMAND_MODE: string;
		GREP_COLORS: string;
		CONDA_EXE: string;
		npm_config_globalconfig: string;
		SSH_AUTH_SOCK: string;
		__CF_USER_TEXT_ENCODING: string;
		VSCODE_PROFILE_INITIALIZED: string;
		npm_execpath: string;
		PAGER: string;
		LESS_TERMCAP_us: string;
		LSCOLORS: string;
		FNM_VERSION_FILE_STRATEGY: string;
		_CE_CONDA: string;
		FNM_ARCH: string;
		PATH: string;
		npm_config_engine_strict: string;
		_: string;
		npm_package_json: string;
		__CFBundleIdentifier: string;
		USER_ZDOTDIR: string;
		CONDA_PREFIX: string;
		npm_config_init_module: string;
		npm_config_userconfig: string;
		PWD: string;
		npm_command: string;
		EDITOR: string;
		npm_lifecycle_event: string;
		npm_package_name: string;
		LANG: string;
		XPC_FLAGS: string;
		VSCODE_GIT_ASKPASS_EXTRA_ARGS: string;
		FNM_MULTISHELL_PATH: string;
		npm_config_npm_version: string;
		npm_config_node_gyp: string;
		npm_package_version: string;
		XPC_SERVICE_NAME: string;
		_CE_M: string;
		VSCODE_INJECTION: string;
		HOME: string;
		SHLVL: string;
		PYENV_SHELL: string;
		VSCODE_GIT_ASKPASS_MAIN: string;
		GOROOT: string;
		HOMEBREW_PREFIX: string;
		FNM_DIR: string;
		LOGNAME: string;
		VSCODEPATH: string;
		CONDA_PYTHON_EXE: string;
		npm_config_cache: string;
		npm_lifecycle_script: string;
		VSCODE_GIT_IPC_HANDLE: string;
		LESS_TERMCAP_so: string;
		GOPATH: string;
		CONDA_DEFAULT_ENV: string;
		FNM_RESOLVE_ENGINES: string;
		npm_config_user_agent: string;
		GIT_ASKPASS: string;
		VSCODE_GIT_ASKPASS_NODE: string;
		HOMEBREW_CELLAR: string;
		INFOPATH: string;
		COLORTERM: string;
		LESS_TERMCAP_se: string;
		npm_config_prefix: string;
		npm_node_execpath: string;
		[key: `PUBLIC_${string}`]: undefined;
		[key: `${string}`]: string | undefined;
	}
}

/**
 * Similar to [`$env/dynamic/private`](https://svelte.dev/docs/kit/$env-dynamic-private), but only includes variables that begin with [`config.kit.env.publicPrefix`](https://svelte.dev/docs/kit/configuration#env) (which defaults to `PUBLIC_`), and can therefore safely be exposed to client-side code.
 * 
 * Note that public dynamic environment variables must all be sent from the server to the client, causing larger network requests — when possible, use `$env/static/public` instead.
 * 
 * Dynamic environment variables cannot be used during prerendering.
 * 
 * ```ts
 * import { env } from '$env/dynamic/public';
 * console.log(env.PUBLIC_DEPLOYMENT_SPECIFIC_VARIABLE);
 * ```
 */
declare module '$env/dynamic/public' {
	export const env: {
		[key: `PUBLIC_${string}`]: string | undefined;
	}
}
